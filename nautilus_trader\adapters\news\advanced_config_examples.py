"""
PANewsDataClient高级配置示例
展示所有可配置参数的使用方法
"""
from nautilus_trader.adapters.news.pa_news_data_client import PANewsDataClientConfig


class AdvancedNewsConfigExamples:
    """高级新闻客户端配置示例"""
    
    @staticmethod
    def custom_proxy_ports_config() -> PANewsDataClientConfig:
        """自定义代理端口配置 - 避免端口冲突"""
        return PANewsDataClientConfig(
            # 基础设置
            scraping_interval=180,  # 3分钟
            max_news_per_request=15,
            request_timeout=30,
            
            # 自定义代理端口（避免与其他服务冲突）
            enable_proxy=True,
            proxy_port=8890,  # 自定义代理端口
            api_port=8090,   # 自定义API端口
            clash_config_dir="./custom_clash",
            enable_rules=True,
            
            proxy_rules=[
                "*.panewslab.com",
                "panewslab.com",
                "*.httpbin.org",
                "*.ipinfo.io"
            ]
        )
    
    @staticmethod
    def custom_clash_binary_config() -> PANewsDataClientConfig:
        """自定义Clash二进制路径配置"""
        return PANewsDataClientConfig(
            # 基础设置
            scraping_interval=240,  # 4分钟
            max_news_per_request=20,
            request_timeout=35,
            
            # 自定义Clash二进制路径
            enable_proxy=True,
            clash_binary_path="/opt/clash/clash-premium",  # 自定义Clash路径
            clash_config_dir="/etc/clash/configs",
            proxy_port=7890,
            api_port=9090,
            enable_rules=True,
            
            proxy_rules=[
                "*.panewslab.com",
                "panewslab.com"
            ]
        )
    
    @staticmethod
    def multi_source_proxy_config() -> PANewsDataClientConfig:
        """多源代理配置 - 使用多个代理源"""
        return PANewsDataClientConfig(
            # 基础设置
            scraping_interval=150,  # 2.5分钟
            max_news_per_request=25,
            request_timeout=25,
            
            # 多源代理配置
            enable_proxy=True,
            clash_config_dir="./multi_source_clash",
            proxy_port=7890,
            api_port=9090,
            enable_rules=True,
            
            # 多个代理源
            custom_sources={
                'clash': [
                    'https://source1.example.com/clash.yml',
                    'https://source2.example.com/clash.yml'
                ],
                'v2ray': [
                    'https://source1.example.com/v2ray',
                    'https://source2.example.com/v2ray'
                ]
            },
            
            # 扩展的代理规则
            proxy_rules=[
                "*.panewslab.com",
                "panewslab.com",
                "*.coindesk.com",
                "*.cointelegraph.com",
                "*.httpbin.org",
                "*.ipinfo.io",
                "*.gstatic.com"
            ]
        )
    
    @staticmethod
    def no_proxy_config() -> PANewsDataClientConfig:
        """无代理配置 - 直连模式"""
        return PANewsDataClientConfig(
            # 基础设置
            scraping_interval=300,  # 5分钟
            max_news_per_request=10,
            request_timeout=45,  # 直连可能需要更长超时
            
            # 禁用代理
            enable_proxy=False,
            
            # 即使禁用代理，也可以设置这些参数（备用）
            clash_config_dir="./clash_configs",
            proxy_port=7890,
            api_port=9090,
            enable_rules=False,  # 无代理时禁用规则
            
            # 直连模式用户代理
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
    
    @staticmethod
    def development_config() -> PANewsDataClientConfig:
        """开发环境配置 - 适用于开发和测试"""
        return PANewsDataClientConfig(
            # 开发设置
            scraping_interval=600,  # 10分钟（开发时不需要太频繁）
            max_news_per_request=5,  # 少量数据便于调试
            request_timeout=60,  # 开发时允许更长超时
            
            # 开发环境代理设置
            enable_proxy=True,
            clash_config_dir="./dev_clash_configs",
            clash_binary_path=None,  # 使用默认路径
            proxy_port=7890,
            api_port=9090,
            enable_rules=True,
            
            proxy_rules=[
                "*.panewslab.com",
                "panewslab.com",
                "*.httpbin.org",  # 用于测试
                "*.postman-echo.com"  # 用于API测试
            ],
            
            # 开发环境用户代理
            user_agent="Mozilla/5.0 (Development) AppleWebKit/537.36"
        )


# 使用示例
def get_config_for_environment(env: str) -> PANewsDataClientConfig:
    """根据环境获取配置"""
    configs = {
        'development': AdvancedNewsConfigExamples.development_config(),
        'production': AdvancedNewsConfigExamples.multi_source_proxy_config(),
        'testing': AdvancedNewsConfigExamples.no_proxy_config(),
        'custom_ports': AdvancedNewsConfigExamples.custom_proxy_ports_config(),
        'custom_binary': AdvancedNewsConfigExamples.custom_clash_binary_config(),
    }
    
    return configs.get(env, AdvancedNewsConfigExamples.development_config())


if __name__ == "__main__":
    # 示例：获取不同环境的配置
    dev_config = get_config_for_environment('development')
    print(f"开发环境配置: 爬取间隔={dev_config.scraping_interval}秒")
    
    prod_config = get_config_for_environment('production')
    print(f"生产环境配置: 代理端口={prod_config.proxy_port}")
    
    custom_config = get_config_for_environment('custom_ports')
    print(f"自定义端口配置: 代理端口={custom_config.proxy_port}, API端口={custom_config.api_port}")
