# PANewsDataClient - 集成CrawlAdapter的新闻数据客户端

## 概述

PANewsDataClient是专为Nautilus Trader设计的新闻数据适配器，集成了CrawlAdapter的SimpleProxyClient，专注于从PANews爬取新闻数据。

## 主要特性

- ✅ **专注新闻爬取**: 只负责新闻数据获取，不包含情感分析
- ✅ **代理管理集成**: 使用CrawlAdapter的SimpleProxyClient处理代理
- ✅ **Nautilus Trader集成**: 完全兼容Nautilus Trader的数据架构
- ✅ **自动化爬取**: 支持定时自动爬取和手动触发
- ✅ **多数据源支持**: 可与Binance等其他数据源同时使用
- ✅ **配置灵活**: 支持多种使用场景的配置模板

## 安装依赖

```bash
# 安装CrawlAdapter
pip install git+https://github.com/graceyangfan/CrawlAdapter.git

# 安装其他依赖
pip install aiohttp beautifulsoup4
```

## 快速开始

### 1. 基本配置

```python
from nautilus_trader.adapters.news.pa_news_data_client import PANewsDataClientConfig

config = PANewsDataClientConfig(
    enable_proxy=True,
    scraping_interval=300,  # 5分钟爬取一次
    max_news_per_request=20,
    proxy_rules=[
        "*.panewslab.com",
        "panewslab.com"
    ]
)
```

### 2. 与Binance数据同时使用

```python
from nautilus_trader.config import TradingNodeConfig
from nautilus_trader.live.node import TradingNode
from nautilus_trader.adapters.binance.factories import BinanceLiveDataClientFactory
from nautilus_trader.adapters.news.pa_news_data_client import PANewsDataClientFactory

# 配置交易节点
config = TradingNodeConfig(
    trader_id="NewsTrader-001",
    data_clients={
        "BINANCE": {
            "api_key": "your_api_key",
            "api_secret": "your_api_secret",
            "account_type": "spot",
        },
        "PANEWS": PANewsDataClientConfig(
            enable_proxy=True,
            scraping_interval=180
        )
    }
)

# 创建节点并注册工厂
node = TradingNode(config=config)
node.add_data_client_factory("BINANCE", BinanceLiveDataClientFactory)
node.add_data_client_factory("PANEWS", PANewsDataClientFactory)
```

### 3. 策略中订阅新闻数据

```python
from nautilus_trader.trading.strategy import Strategy
from nautilus_trader.model.data import DataType
from nautilus_trader.adapters.news.pa_news_data_client import PANewsData

class NewsStrategy(Strategy):
    def on_start(self):
        # 订阅价格数据
        self.subscribe_trade_ticks(
            instrument_id=InstrumentId.from_str("BTCUSDT.BINANCE"),
            client_id=ClientId("BINANCE")
        )
        
        # 订阅新闻数据
        self.subscribe_data(
            data_type=DataType(PANewsData),
            client_id=ClientId("PANEWS")
        )
    
    def on_data(self, data):
        if isinstance(data, PANewsData):
            self.log.info(f"收到新闻: {data.title}")
            
            # 检查是否为加密货币相关
            if data.is_crypto_related():
                symbols = data.get_symbols_list()
                self.log.info(f"涉及品种: {symbols}")
```

## 配置选项

### 基础配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `base_url` | str | "https://www.panewslab.com" | PANews基础URL |
| `scraping_interval` | int | 300 | 爬取间隔（秒） |
| `max_news_per_request` | int | 20 | 每次请求最大新闻数 |
| `request_timeout` | int | 30 | 请求超时时间 |

### 代理配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enable_proxy` | bool | True | 是否启用代理 |
| `proxy_rules` | List[str] | 自动设置 | 代理规则列表 |
| `custom_sources` | Dict | None | 自定义代理源 |
| `clash_config_dir` | str | "./clash_configs" | Clash配置目录 |
| `clash_binary_path` | str | None | Clash二进制文件路径（可选） |
| `proxy_port` | int | 7890 | 代理服务端口 |
| `api_port` | int | 9090 | 代理API端口 |
| `enable_rules` | bool | True | 是否启用代理规则 |

## 配置模板

### 1. 高频交易配置

```python
from nautilus_trader.adapters.news.config_examples import NewsConfigTemplates

config = NewsConfigTemplates.high_frequency_config()
# 1分钟爬取一次，适用于高频交易
```

### 2. 自定义代理配置

```python
from nautilus_trader.adapters.news.pa_news_data_client import PANewsDataClientConfig

# 自定义代理端口和路径
config = PANewsDataClientConfig(
    scraping_interval=120,  # 2分钟爬取一次
    enable_proxy=True,

    # 自定义代理配置
    clash_config_dir="/custom/clash/configs",
    clash_binary_path="/usr/local/bin/clash",  # 自定义Clash路径
    proxy_port=7891,  # 自定义代理端口
    api_port=9091,  # 自定义API端口
    enable_rules=True,  # 启用规则

    # 自定义代理源
    custom_sources={
        'clash': [
            'https://your-clash-subscription.com/config.yml'
        ]
    }
)
```

### 3. 生产环境配置

```python
config = NewsConfigTemplates.production_config()
# 3分钟爬取一次，稳定性优先
```

### 3. 自定义代理配置

```python
config = NewsConfigTemplates.custom_proxy_config(
    clash_sources=[
        "https://your-clash-source.com/config.yml"
    ],
    proxy_rules=[
        "*.panewslab.com",
        "*.coindesk.com"
    ]
)
```

## 数据结构

### PANewsData

```python
@customdataclass
class PANewsData(Data):
    title: str              # 新闻标题
    content: str            # 新闻内容
    url: str               # 新闻链接
    publish_time: str      # 发布时间
    symbols: str           # 相关交易品种（逗号分隔）
    category: str          # 新闻分类
    source: str            # 数据源
    news_id: str           # 新闻ID
    
    def get_symbols_list(self) -> List[str]:
        """获取交易品种列表"""
        
    def is_crypto_related(self) -> bool:
        """判断是否为加密货币相关新闻"""
```

## 代理管理

### 代理状态检查

```python
# 获取代理状态
status = await client.get_proxy_status()
print(f"代理状态: {status}")
```

### 手动切换代理

```python
# 切换代理
success = await client.switch_proxy()
print(f"切换结果: {success}")
```

### 手动爬取

```python
# 手动触发一次爬取
news_items = await client.manual_scrape()
print(f"获取 {len(news_items)} 条新闻")
```

## 使用场景

### 1. 新闻驱动交易

监控特定关键词的新闻，触发交易信号：

```python
def on_data(self, data):
    if isinstance(data, PANewsData):
        if 'SEC' in data.title.upper() or '监管' in data.title:
            self.log.warning(f"监管新闻: {data.title}")
            # 执行风险管理逻辑
```

### 2. 多品种新闻过滤

根据交易品种过滤相关新闻：

```python
def on_data(self, data):
    if isinstance(data, PANewsData):
        symbols = data.get_symbols_list()
        if 'BTC' in symbols:
            # 处理比特币相关新闻
            pass
        if 'ETH' in symbols:
            # 处理以太坊相关新闻
            pass
```

## 注意事项

1. **代理配置**: 确保CrawlAdapter正确安装并配置代理源
2. **爬取频率**: 合理设置爬取间隔，避免对服务器造成压力
3. **错误处理**: 客户端内置重试机制，但仍需监控连接状态
4. **数据去重**: 客户端自动处理重复新闻，基于news_id去重

## 故障排除

### 1. CrawlAdapter导入失败

```bash
pip install git+https://github.com/graceyangfan/CrawlAdapter.git
```

### 2. 代理连接失败

检查代理源配置和网络连接：

```python
config = PANewsDataClientConfig(
    enable_proxy=False  # 临时禁用代理测试
)
```

### 3. 新闻数据为空

检查PANews API是否可访问：

```python
# 手动测试API
import aiohttp
async with aiohttp.ClientSession() as session:
    async with session.get("https://www.panewslab.com/webapi/flashnews?LId=1&Rn=5&tw=0") as response:
        data = await response.json()
        print(data)
```

## 完整示例

参考 `example_usage.py` 文件获取完整的使用示例。
