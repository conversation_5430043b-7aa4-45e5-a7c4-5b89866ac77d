"""
PANewsDataClient配置示例
提供不同场景下的配置模板
"""
from typing import Dict, List, Optional
from nautilus_trader.adapters.news.pa_news_data_client import PANewsDataClientConfig


class NewsConfigTemplates:
    """新闻客户端配置模板"""
    
    @staticmethod
    def basic_config() -> PANewsDataClientConfig:
        """基础配置 - 适用于简单使用场景"""
        return PANewsDataClientConfig(
            # 基础设置
            base_url="https://www.panewslab.com",
            scraping_interval=300,  # 5分钟
            max_news_per_request=20,
            request_timeout=30,

            # 代理设置
            enable_proxy=True,
            proxy_rules=[
                "*.panewslab.com",
                "panewslab.com"
            ],
            clash_config_dir="./clash_configs",
            clash_binary_path=None,  # 使用默认Clash路径
            proxy_port=7890,  # 默认代理端口
            api_port=9090,  # 默认API端口
            enable_rules=True,  # 启用代理规则

            # 请求设置
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        )
    
    @staticmethod
    def high_frequency_config() -> PANewsDataClientConfig:
        """高频配置 - 适用于需要快速获取新闻的场景"""
        return PANewsDataClientConfig(
            # 高频设置
            scraping_interval=60,   # 1分钟
            max_news_per_request=50,
            request_timeout=15,     # 更短的超时
            
            # 代理设置
            enable_proxy=True,
            proxy_rules=[
                "*.panewslab.com",
                "panewslab.com",
                "*.httpbin.org",    # 用于健康检查
                "*.ipinfo.io"       # 用于IP检测
            ],
            
            # 代理客户端配置
            clash_config_dir="./clash_configs",
            clash_binary_path=None,  # 使用默认路径
            proxy_port=7890,  # 可根据需要调整
            api_port=9090,  # 可根据需要调整
            enable_rules=True,  # 高频交易建议启用规则

            # 自定义代理源（需要用户提供）
            custom_sources={
                'clash': [
                    # 'https://your-clash-source.com/config.yml'
                ],
                'v2ray': [
                    # 'https://your-v2ray-source.com/subscription'
                ]
            },

            # 高频交易用户代理
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        )
    
    @staticmethod
    def production_config() -> PANewsDataClientConfig:
        """生产环境配置 - 适用于实盘交易"""
        return PANewsDataClientConfig(
            # 稳定性优先设置
            scraping_interval=180,  # 3分钟
            max_news_per_request=30,
            request_timeout=45,     # 更长的超时以确保稳定性
            
            # 代理设置
            enable_proxy=True,
            proxy_rules=[
                "*.panewslab.com",
                "panewslab.com",
                "*.coindesk.com",   # 可能的其他新闻源
                "*.cointelegraph.com"
            ],
            clash_config_dir="./production_clash_configs",
            clash_binary_path=None,  # 生产环境可能需要指定特定路径
            proxy_port=7890,  # 生产环境代理端口
            api_port=9090,  # 生产环境API端口
            enable_rules=True,  # 生产环境建议启用规则

            # 生产环境用户代理
            user_agent="Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        )
    
    @staticmethod
    def no_proxy_config() -> PANewsDataClientConfig:
        """无代理配置 - 适用于网络环境良好的场景"""
        return PANewsDataClientConfig(
            # 基础设置
            scraping_interval=240,  # 4分钟
            max_news_per_request=25,
            request_timeout=20,
            
            # 禁用代理
            enable_proxy=False,
            proxy_rules=[],
            
            # 标准用户代理
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        )
    
    @staticmethod
    def custom_proxy_config(
        clash_sources: List[str] = None,
        v2ray_sources: List[str] = None,
        proxy_rules: List[str] = None
    ) -> PANewsDataClientConfig:
        """自定义代理配置"""
        
        # 默认代理规则
        if proxy_rules is None:
            proxy_rules = [
                "*.panewslab.com",
                "panewslab.com",
                "*.httpbin.org",
                "*.ipinfo.io"
            ]
        
        # 构建自定义源
        custom_sources = {}
        if clash_sources:
            custom_sources['clash'] = clash_sources
        if v2ray_sources:
            custom_sources['v2ray'] = v2ray_sources
        
        return PANewsDataClientConfig(
            # 平衡设置
            scraping_interval=150,  # 2.5分钟
            max_news_per_request=35,
            request_timeout=25,
            
            # 自定义代理设置
            enable_proxy=True,
            proxy_rules=proxy_rules,
            custom_sources=custom_sources if custom_sources else None,
            clash_config_dir="./custom_clash_configs"
        )


class ProxySourceExamples:
    """代理源配置示例"""
    
    @staticmethod
    def get_clash_sources_example() -> List[str]:
        """Clash配置源示例（需要用户替换为实际URL）"""
        return [
            # "https://example.com/clash-config.yml",
            # "https://another-provider.com/config.yaml",
            # "https://third-provider.com/clash.yml"
        ]
    
    @staticmethod
    def get_v2ray_sources_example() -> List[str]:
        """V2Ray订阅源示例（需要用户替换为实际URL）"""
        return [
            # "https://example.com/v2ray-subscription",
            # "https://another-provider.com/vmess",
            # "https://third-provider.com/subscription"
        ]
    
    @staticmethod
    def get_proxy_rules_examples() -> Dict[str, List[str]]:
        """代理规则示例"""
        return {
            "news_sites": [
                "*.panewslab.com",
                "*.coindesk.com",
                "*.cointelegraph.com",
                "*.decrypt.co",
                "*.theblock.co"
            ],
            "api_endpoints": [
                "*.panewslab.com",
                "panewslab.com",
                "api.panewslab.com"
            ],
            "testing_sites": [
                "*.httpbin.org",
                "*.ipinfo.io",
                "*.ifconfig.co",
                "*.detectportal.firefox.com"
            ],
            "comprehensive": [
                # 新闻站点
                "*.panewslab.com",
                "*.coindesk.com",
                "*.cointelegraph.com",
                # 测试站点
                "*.httpbin.org",
                "*.ipinfo.io",
                # 连通性测试
                "*.gstatic.com",
                "*.detectportal.firefox.com"
            ]
        }


def create_config_for_scenario(scenario: str) -> PANewsDataClientConfig:
    """根据场景创建配置"""
    
    scenarios = {
        "basic": NewsConfigTemplates.basic_config,
        "high_frequency": NewsConfigTemplates.high_frequency_config,
        "production": NewsConfigTemplates.production_config,
        "no_proxy": NewsConfigTemplates.no_proxy_config,
    }
    
    if scenario not in scenarios:
        raise ValueError(f"未知场景: {scenario}. 可用场景: {list(scenarios.keys())}")
    
    return scenarios[scenario]()


def print_config_examples():
    """打印配置示例"""
    print("PANewsDataClient 配置示例")
    print("=" * 50)
    
    print("\n1. 基础配置:")
    basic = NewsConfigTemplates.basic_config()
    print(f"  爬取间隔: {basic.scraping_interval}秒")
    print(f"  每次最大新闻数: {basic.max_news_per_request}")
    print(f"  启用代理: {basic.enable_proxy}")
    print(f"  代理规则数量: {len(basic.proxy_rules)}")
    
    print("\n2. 高频配置:")
    high_freq = NewsConfigTemplates.high_frequency_config()
    print(f"  爬取间隔: {high_freq.scraping_interval}秒")
    print(f"  每次最大新闻数: {high_freq.max_news_per_request}")
    print(f"  请求超时: {high_freq.request_timeout}秒")
    
    print("\n3. 生产环境配置:")
    prod = NewsConfigTemplates.production_config()
    print(f"  爬取间隔: {prod.scraping_interval}秒")
    print(f"  配置目录: {prod.clash_config_dir}")
    print(f"  代理规则: {prod.proxy_rules}")
    
    print("\n4. 无代理配置:")
    no_proxy = NewsConfigTemplates.no_proxy_config()
    print(f"  启用代理: {no_proxy.enable_proxy}")
    print(f"  爬取间隔: {no_proxy.scraping_interval}秒")
    
    print("\n5. 代理规则示例:")
    rules = ProxySourceExamples.get_proxy_rules_examples()
    for category, rule_list in rules.items():
        print(f"  {category}: {len(rule_list)} 条规则")


if __name__ == "__main__":
    print_config_examples()
    
    print("\n" + "=" * 50)
    print("创建自定义配置示例:")
    
    # 创建自定义配置
    custom_config = NewsConfigTemplates.custom_proxy_config(
        clash_sources=[
            # "https://your-clash-source.com/config.yml"
        ],
        proxy_rules=[
            "*.panewslab.com",
            "*.example.com"
        ]
    )
    
    print(f"自定义配置创建成功:")
    print(f"  爬取间隔: {custom_config.scraping_interval}秒")
    print(f"  代理规则: {custom_config.proxy_rules}")
    print(f"  自定义源: {custom_config.custom_sources}")
