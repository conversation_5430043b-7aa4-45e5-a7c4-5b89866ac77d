"""
简化的新闻策略 - 只订阅新闻爬取并打印最新新闻
"""
from nautilus_trader.trading.strategy import Strategy, StrategyConfig
from nautilus_trader.model.data import DataType, CustomData
from nautilus_trader.model.identifiers import ClientId
from nautilus_trader.core.data import Data
from pa_news_data_client import PANewsData


class SimpleNewsStrategyConfig(StrategyConfig, frozen=True):
    """简化新闻策略配置"""
    news_client_id: str = "PANEWS"


class SimpleNewsStrategy(Strategy):
    """
    简化的新闻策略
    
    功能：
    1. 只订阅PANews新闻数据
    2. 接收CustomData格式的新闻
    3. 打印最新新闻标题和内容
    """
    
    def __init__(self, config: SimpleNewsStrategyConfig):
        super().__init__(config)
        
        # 配置
        self.news_client_id = ClientId(config.news_client_id)
        
        # 状态
        self.news_count = 0
    
    def on_start(self):
        """策略启动时订阅新闻数据"""
        self.log.info("🚀 启动简化新闻策略")
        
        # 只订阅新闻数据（CustomData格式）
        self.subscribe_data(
            data_type=DataType(PANewsData, metadata={"source": "PANews"}),
            client_id=self.news_client_id
        )
        
        self.log.info(f"✅ 已订阅新闻数据，客户端: {self.news_client_id}")
    
    def on_data(self, data: Data):
        """处理接收到的数据"""
        # 检查是否为CustomData格式的新闻
        if isinstance(data, CustomData):
            # 获取包装的新闻数据
            news_data = data.data
            
            if isinstance(news_data, PANewsData):
                self.news_count += 1
                self._print_news(news_data, self.news_count)
    
    def _print_news(self, news: PANewsData, count: int):
        """打印新闻信息"""
        self.log.info("=" * 80)
        self.log.info(f"📰 新闻 #{count}")
        self.log.info("=" * 80)
        self.log.info(f"🏷️  标题: {news.title}")
        self.log.info(f"⏰ 时间: {news.publish_time}")
        self.log.info(f"🔗 链接: {news.url}")
        
        # 显示内容（限制长度）
        content_preview = news.content[:200] + "..." if len(news.content) > 200 else news.content
        self.log.info(f"📝 内容: {content_preview}")
        
        # 显示相关交易品种
        if news.symbols:
            symbols_list = news.get_symbols_list()
            self.log.info(f"💰 相关品种: {', '.join(symbols_list)}")
        
        # 显示分类
        if news.category:
            self.log.info(f"📂 分类: {news.category}")
        
        # 显示是否为加密货币相关
        if news.is_crypto_related():
            self.log.info("🪙 加密货币相关新闻")
        
        self.log.info("=" * 80)
    
    def on_stop(self):
        """策略停止"""
        self.log.info(f"🛑 策略停止，共处理 {self.news_count} 条新闻")
    
    def on_reset(self):
        """策略重置"""
        self.news_count = 0
        self.log.info("🔄 策略已重置")
    
    def on_dispose(self):
        """策略销毁"""
        self.log.info("🗑️ 策略已销毁")


# 使用示例
if __name__ == "__main__":
    import asyncio
    from nautilus_trader.config import TradingNodeConfig
    from nautilus_trader.live.node import TradingNode
    from nautilus_trader.trading.config import ImportableStrategyConfig
    from pa_news_data_client import (
        PANewsDataClientFactory,
        PANewsDataClientConfig
    )

    async def run_simple_news_strategy():
        """运行简化新闻策略示例"""
        print("🚀 启动简化新闻策略示例")

        # 创建新闻客户端配置（用户可设置爬取间隔和代理参数）
        news_config = PANewsDataClientConfig(
            scraping_interval=120,  # 用户设置：2分钟爬取一次
            enable_proxy=True,
            max_news_per_request=3,  # 只获取最新3条新闻

            # 用户可自定义的代理配置
            clash_config_dir="./clash_configs",  # Clash配置目录
            clash_binary_path=None,  # 如果需要自定义Clash二进制路径
            proxy_port=7890,  # 代理端口
            api_port=9090,  # API端口
            enable_rules=True,  # 启用代理规则
        )

        # 创建策略配置 - 使用ImportableStrategyConfig
        strategy_config = ImportableStrategyConfig(
            strategy_path="simple_news_strategy:SimpleNewsStrategy",
            config_path="simple_news_strategy:SimpleNewsStrategyConfig",
            config={
                "news_client_id": "PANEWS"
            }
        )

        # 创建交易节点配置
        config = TradingNodeConfig(
            trader_id="SimpleNewsTrader-001",
            data_clients={
                "PANEWS": news_config
            },
            strategies=[strategy_config]  # 传递ImportableStrategyConfig对象列表
        )

        # 创建交易节点
        node = TradingNode(config=config)

        # 注册新闻数据客户端工厂
        node.add_data_client_factory("PANEWS", PANewsDataClientFactory)

        # 构建并启动
        node.build()

        try:
            print("✅ 节点已启动，等待新闻数据...")
            print("📝 策略将打印接收到的最新新闻")
            print("⏹️  按 Ctrl+C 停止")

            await node.run_async()

        except KeyboardInterrupt:
            print("\n⏹️ 用户中断")
        finally:
            node.stop()
            print("✅ 节点已停止")

    # 运行示例
    asyncio.run(run_simple_news_strategy())
